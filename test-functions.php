<?php
/**
 * Test Functions Loading
 * Quick test to check if all required functions are loading properly
 */

// Enable all error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Function Loading Test</h1>";
echo "<p>Testing function availability...</p>";

// Test 1: Load config
echo "<h2>1. Loading Configuration</h2>";
try {
    require_once __DIR__ . '/config/app.php';
    echo "✅ Config loaded successfully<br>";
    echo "Environment: " . (isProduction() ? 'Production' : 'Development') . "<br>";
} catch (Exception $e) {
    echo "❌ Config failed: " . $e->getMessage() . "<br>";
    exit;
}

// Test 2: Check database
echo "<h2>2. Database Connection</h2>";
if (isset($database)) {
    echo "✅ Database object exists<br>";
    try {
        $test = $database->fetch("SELECT 1 as test");
        echo "✅ Database connection working<br>";
    } catch (Exception $e) {
        echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ Database object not found<br>";
}

// Test 3: Check critical functions
echo "<h2>3. Critical Functions</h2>";
$functions = [
    'getPackageServices' => 'Package services function',
    'createCustomerBooking' => 'Customer booking function',
    'isLoggedIn' => 'Authentication check',
    'formatCurrency' => 'Currency formatting',
    'getBasePath' => 'Base path function',
    'redirect' => 'Redirect function'
];

foreach ($functions as $func => $description) {
    if (function_exists($func)) {
        echo "✅ $description ($func)<br>";
    } else {
        echo "❌ $description ($func) - MISSING<br>";
    }
}

// Test 4: Test getPackageServices specifically
echo "<h2>4. Testing getPackageServices Function</h2>";
if (function_exists('getPackageServices')) {
    try {
        // Try to call the function with a test ID
        $result = getPackageServices(1);
        echo "✅ getPackageServices function callable<br>";
        echo "Result type: " . gettype($result) . "<br>";
        if (is_array($result)) {
            echo "Result count: " . count($result) . " services<br>";
        }
    } catch (Exception $e) {
        echo "❌ getPackageServices error: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ getPackageServices function not found<br>";
}

// Test 5: Check packages table
echo "<h2>5. Database Tables</h2>";
$tables = ['packages', 'services', 'package_services', 'users', 'bookings'];
foreach ($tables as $table) {
    try {
        $count = $database->fetch("SELECT COUNT(*) as count FROM $table")['count'];
        echo "✅ Table '$table' exists with $count records<br>";
    } catch (Exception $e) {
        echo "❌ Table '$table' error: " . $e->getMessage() . "<br>";
    }
}

// Test 6: Test packages query
echo "<h2>6. Testing Packages Query</h2>";
try {
    $packages = $database->fetchAll("
        SELECT
            p.*,
            COALESCE(SUM(s.price), 0) as original_price,
            COALESCE(SUM(s.duration), 0) as duration,
            COUNT(ps.service_id) as service_count
        FROM packages p
        LEFT JOIN package_services ps ON p.id = ps.package_id
        LEFT JOIN services s ON ps.service_id = s.id AND s.is_active = 1
        WHERE p.is_active = 1
        GROUP BY p.id
        ORDER BY p.price ASC
        LIMIT 1
    ");
    echo "✅ Packages query successful<br>";
    echo "Found " . count($packages) . " packages<br>";
    
    if (!empty($packages)) {
        $package = $packages[0];
        echo "First package: " . htmlspecialchars($package['name']) . "<br>";
        
        // Test getPackageServices with real package ID
        try {
            $services = getPackageServices($package['id']);
            echo "✅ getPackageServices worked for package ID " . $package['id'] . "<br>";
            echo "Found " . count($services) . " services in package<br>";
        } catch (Exception $e) {
            echo "❌ getPackageServices failed for package ID " . $package['id'] . ": " . $e->getMessage() . "<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Packages query failed: " . $e->getMessage() . "<br>";
}

// Test 7: Check file permissions
echo "<h2>7. File Permissions</h2>";
$files = [
    'logs/error.log',
    'logs/debug.log',
    'includes/package_functions.php',
    'config/app.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        $readable = is_readable($file) ? 'readable' : 'not readable';
        $writable = is_writable($file) ? 'writable' : 'not writable';
        echo "✅ $file exists ($readable, $writable)<br>";
    } else {
        echo "❌ $file does not exist<br>";
    }
}

echo "<h2>Test Complete</h2>";
echo "<p><a href='debug-errors.php'>View Full Debug Page</a></p>";
echo "<p><a href='packages.php'>Test Packages Page</a></p>";
echo "<p><a href='customer/book/'>Test Customer Booking</a></p>";
echo "<p><a href='admin/'>Test Admin Panel</a></p>";
?>
