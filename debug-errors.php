<?php
/**
 * Debug Error Checker
 * Helps identify PHP errors and issues in the application
 */

// Enable all error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Start output buffering to catch any errors
ob_start();

try {
    require_once __DIR__ . '/config/app.php';
    $configLoaded = true;
} catch (Exception $e) {
    $configLoaded = false;
    $configError = $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Error Checker - Flix Salon & SPA</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
            border-left: 4px solid #d4af37;
        }
        .section {
            background: #2a2a2a;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #d4af37;
        }
        .success {
            border-left-color: #28a745;
            background: #1e3a1e;
        }
        .error {
            border-left-color: #dc3545;
            background: #3a1e1e;
        }
        .warning {
            border-left-color: #ffc107;
            background: #3a3a1e;
        }
        .info {
            border-left-color: #17a2b8;
            background: #1e2a3a;
        }
        .code {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #444;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .status {
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
            font-size: 12px;
        }
        .status.pass {
            background: #28a745;
            color: white;
        }
        .status.fail {
            background: #dc3545;
            color: white;
        }
        .status.warn {
            background: #ffc107;
            color: black;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #d4af37;
            color: #000;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 5px;
        }
        .btn:hover {
            background: #b8941f;
        }
        .log-content {
            max-height: 300px;
            overflow-y: auto;
            background: #000;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Debug Error Checker</h1>
            <p>Comprehensive error detection and debugging tool for Flix Salon & SPA</p>
            <p><strong>Current Time:</strong> <?= date('Y-m-d H:i:s') ?></p>
        </div>

        <!-- Configuration Test -->
        <div class="section <?= $configLoaded ? 'success' : 'error' ?>">
            <h2>📋 Configuration Status</h2>
            <?php if ($configLoaded): ?>
                <div class="test-item">
                    <span>Config file loaded</span>
                    <span class="status pass">PASS</span>
                </div>
                <div class="test-item">
                    <span>Environment detection</span>
                    <span class="status <?= isProduction() ? 'warn' : 'pass' ?>"><?= isProduction() ? 'PRODUCTION' : 'DEVELOPMENT' ?></span>
                </div>
                <div class="test-item">
                    <span>Database connection</span>
                    <span class="status <?= isset($database) ? 'pass' : 'fail' ?>"><?= isset($database) ? 'CONNECTED' : 'FAILED' ?></span>
                </div>
            <?php else: ?>
                <div class="error">
                    <strong>Configuration Error:</strong>
                    <div class="code"><?= htmlspecialchars($configError) ?></div>
                </div>
            <?php endif; ?>
        </div>

        <!-- File Existence Tests -->
        <div class="section info">
            <h2>📁 Critical Files Check</h2>
            <?php
            $criticalFiles = [
                'config/app.php' => 'Main configuration',
                'config/database.php' => 'Database configuration',
                'includes/package_functions.php' => 'Package functions',
                'includes/customer_panel_functions.php' => 'Customer functions',
                'includes/auth.php' => 'Authentication class',
                'customer/book/index.php' => 'Customer booking page',
                'admin/index.php' => 'Admin dashboard',
                'packages.php' => 'Packages page'
            ];

            foreach ($criticalFiles as $file => $description): ?>
                <div class="test-item">
                    <span><?= $description ?> (<?= $file ?>)</span>
                    <span class="status <?= file_exists($file) ? 'pass' : 'fail' ?>"><?= file_exists($file) ? 'EXISTS' : 'MISSING' ?></span>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Function Tests -->
        <?php if ($configLoaded): ?>
        <div class="section info">
            <h2>⚙️ Function Availability</h2>
            <?php
            $functions = [
                'getPackageServices' => 'Package services function',
                'createCustomerBooking' => 'Customer booking function',
                'isLoggedIn' => 'Authentication check',
                'formatCurrency' => 'Currency formatting',
                'getBasePath' => 'Base path function',
                'redirect' => 'Redirect function'
            ];

            foreach ($functions as $func => $description): ?>
                <div class="test-item">
                    <span><?= $description ?> (<?= $func ?>)</span>
                    <span class="status <?= function_exists($func) ? 'pass' : 'fail' ?>"><?= function_exists($func) ? 'AVAILABLE' : 'MISSING' ?></span>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <!-- Database Tests -->
        <?php if ($configLoaded && isset($database)): ?>
        <div class="section info">
            <h2>🗄️ Database Tests</h2>
            <?php
            try {
                $tables = ['users', 'packages', 'services', 'bookings', 'package_services'];
                foreach ($tables as $table):
                    try {
                        $count = $database->fetch("SELECT COUNT(*) as count FROM $table")['count'];
                        $status = 'pass';
                        $message = "EXISTS ($count records)";
                    } catch (Exception $e) {
                        $status = 'fail';
                        $message = 'ERROR: ' . $e->getMessage();
                    }
                ?>
                    <div class="test-item">
                        <span>Table: <?= $table ?></span>
                        <span class="status <?= $status ?>"><?= $message ?></span>
                    </div>
                <?php endforeach; ?>
            } catch (Exception $e): ?>
                <div class="error">Database test failed: <?= htmlspecialchars($e->getMessage()) ?></div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <!-- Error Logs -->
        <div class="section warning">
            <h2>📋 Recent Error Logs</h2>
            <?php
            $logFiles = ['logs/error.log', 'logs/debug.log'];
            foreach ($logFiles as $logFile):
                if (file_exists($logFile)):
                    $logContent = file_get_contents($logFile);
                    $lines = explode("\n", $logContent);
                    $recentLines = array_slice($lines, -20); // Last 20 lines
            ?>
                <h3><?= basename($logFile) ?></h3>
                <div class="log-content"><?= htmlspecialchars(implode("\n", $recentLines)) ?></div>
            <?php else: ?>
                <p>Log file <?= $logFile ?> not found or empty.</p>
            <?php endif; endforeach; ?>
        </div>

        <!-- Test Specific Pages -->
        <div class="section info">
            <h2>🧪 Page Tests</h2>
            <p>Click the buttons below to test specific pages that are experiencing 500 errors:</p>
            <a href="packages.php" class="btn" target="_blank">Test Packages Page</a>
            <a href="customer/book/" class="btn" target="_blank">Test Customer Booking</a>
            <a href="admin/" class="btn" target="_blank">Test Admin Panel</a>
            <a href="test-session.php" class="btn" target="_blank">Test Session Debug</a>
        </div>

        <!-- Actions -->
        <div class="section">
            <h2>🔧 Debug Actions</h2>
            <a href="?clear_logs=1" class="btn">Clear Error Logs</a>
            <a href="?refresh=1" class="btn">Refresh Tests</a>
            <a href="/" class="btn">Return to Homepage</a>
        </div>
    </div>

    <?php
    // Handle actions
    if (isset($_GET['clear_logs'])) {
        $logFiles = ['logs/error.log', 'logs/debug.log'];
        foreach ($logFiles as $logFile) {
            if (file_exists($logFile)) {
                file_put_contents($logFile, '');
            }
        }
        echo "<script>alert('Error logs cleared!'); window.location.href = 'debug-errors.php';</script>";
    }
    ?>
</body>
</html>
