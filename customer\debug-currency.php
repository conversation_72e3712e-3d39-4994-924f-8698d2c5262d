<?php
require_once __DIR__ . '/../config/app.php';

// Check if user is customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    redirect('/auth/login.php');
}

header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Currency Debug - Customer Portal</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .section {
            background: #2a2a2a;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #d4af37;
        }
        .error {
            border-left-color: #dc3545;
            background: #3a1e1e;
        }
        .success {
            border-left-color: #28a745;
            background: #1e3a1e;
        }
        .code {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Currency Debug - Customer Portal</h1>
        
        <div class="section">
            <h2>PHP Constants</h2>
            <div class="code">
CURRENCY_SYMBOL: <?= var_export(CURRENCY_SYMBOL, true) ?>
CURRENCY_CODE: <?= var_export(CURRENCY_CODE, true) ?>
            </div>
        </div>

        <div class="section">
            <h2>Database Settings</h2>
            <div class="code">
<?php
try {
    $currencySymbol = getSetting('business', 'currency_symbol', 'NOT_FOUND');
    $currencyCode = getSetting('business', 'currency_code', 'NOT_FOUND');
    
    echo "getSetting('business', 'currency_symbol'): " . var_export($currencySymbol, true) . "\n";
    echo "getSetting('business', 'currency_code'): " . var_export($currencyCode, true) . "\n";
    
    // Check for any 262145 values in settings
    $corruptedSettings = $database->fetchAll("
        SELECT category, setting_key, setting_value 
        FROM settings 
        WHERE setting_value = '262145' OR setting_value = 262145
    ");
    
    if (!empty($corruptedSettings)) {
        echo "\nCorrupted settings found:\n";
        foreach ($corruptedSettings as $setting) {
            echo "- {$setting['category']}.{$setting['setting_key']} = {$setting['setting_value']}\n";
        }
    } else {
        echo "\nNo corrupted 262145 values found in settings table.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
            </div>
        </div>

        <div class="section">
            <h2>formatCurrency() Function Test</h2>
            <div class="code">
<?php
$testAmounts = [35000, 50000, 100000, 262145];
foreach ($testAmounts as $amount) {
    echo "formatCurrency($amount) = " . formatCurrency($amount) . "\n";
}
?>
            </div>
        </div>

        <div class="section">
            <h2>Direct PHP Output Test</h2>
            <div class="code">
Direct CURRENCY_SYMBOL usage: <?= CURRENCY_SYMBOL ?> 50,000
Direct constant with number_format: <?= CURRENCY_SYMBOL ?> <?= number_format(50000) ?>
            </div>
        </div>

        <div class="section">
            <h2>JavaScript Test</h2>
            <div id="js-test" class="code">Loading JavaScript test...</div>
        </div>

        <div class="section">
            <h2>Live Customer Portal Test</h2>
            <p>Test the actual customer portal elements:</p>
            
            <!-- Simulate customer dashboard elements -->
            <div style="background: #333; padding: 15px; border-radius: 5px; margin: 10px 0;">
                <h3>Total Spent (like in dashboard)</h3>
                <p style="font-size: 18px; font-weight: bold;">
                    <?= CURRENCY_SYMBOL ?> <?= number_format(150000, 2) ?>
                </p>
            </div>
            
            <div style="background: #333; padding: 15px; border-radius: 5px; margin: 10px 0;">
                <h3>Using formatCurrency function</h3>
                <p style="font-size: 18px; font-weight: bold;">
                    <?= formatCurrency(150000) ?>
                </p>
            </div>
        </div>

        <div class="section">
            <h2>Quick Fix</h2>
            <?php if (isset($_POST['fix_currency'])): ?>
                <?php
                try {
                    // Apply fix
                    $fixed = false;
                    
                    // Check if currency_symbol setting exists and fix if corrupted
                    $currentSymbol = getSetting('business', 'currency_symbol', null);
                    if ($currentSymbol === '262145' || $currentSymbol === 262145) {
                        setSetting('business', 'currency_symbol', 'TSH');
                        echo "<div class='success'>✅ Fixed currency_symbol setting: 262145 → TSH</div>";
                        $fixed = true;
                    }
                    
                    // Check if currency_code setting exists and fix if corrupted
                    $currentCode = getSetting('business', 'currency_code', null);
                    if ($currentCode === '262145' || $currentCode === 262145) {
                        setSetting('business', 'currency_code', 'TZS');
                        echo "<div class='success'>✅ Fixed currency_code setting: 262145 → TZS</div>";
                        $fixed = true;
                    }
                    
                    if (!$fixed) {
                        echo "<div class='success'>✅ No corrupted currency settings found to fix.</div>";
                    }
                    
                    echo "<p><a href='?' style='color: #d4af37;'>Refresh page to see results</a></p>";
                    
                } catch (Exception $e) {
                    echo "<div class='error'>❌ Error applying fix: " . htmlspecialchars($e->getMessage()) . "</div>";
                }
                ?>
            <?php else: ?>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="fix_currency" value="1">
                    <button type="submit" style="background: #d4af37; color: #000; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
                        🔧 Apply Currency Fix
                    </button>
                </form>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Test JavaScript currency formatting
        function testJavaScript() {
            const testResults = [];
            
            // Test 1: Direct constant usage (if available)
            try {
                const phpCurrencySymbol = '<?= CURRENCY_SYMBOL ?>';
                testResults.push(`PHP CURRENCY_SYMBOL in JS: "${phpCurrencySymbol}"`);
            } catch (e) {
                testResults.push(`Error getting PHP constant: ${e.message}`);
            }
            
            // Test 2: Check if there's a JavaScript formatCurrency function
            if (typeof formatCurrency === 'function') {
                testResults.push(`formatCurrency(50000): ${formatCurrency(50000)}`);
            } else {
                testResults.push('No JavaScript formatCurrency function found');
            }
            
            // Test 3: Manual formatting
            testResults.push(`Manual format: TSH ${(50000).toLocaleString()}`);
            
            document.getElementById('js-test').textContent = testResults.join('\n');
        }
        
        // Run test when page loads
        document.addEventListener('DOMContentLoaded', testJavaScript);
    </script>
</body>
</html>
