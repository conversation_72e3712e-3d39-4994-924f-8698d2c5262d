<?php
/**
 * Fix 262145 Issue
 * Comprehensive fix for the currency display issue
 */

require_once __DIR__ . '/config/app.php';

header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix 262145 Issue - Flix Salon & SPA</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
            border-left: 4px solid #d4af37;
        }
        .section {
            background: #2a2a2a;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #d4af37;
        }
        .error {
            border-left-color: #dc3545;
            background: #3a1e1e;
        }
        .warning {
            border-left-color: #ffc107;
            background: #3a3a1e;
        }
        .success {
            border-left-color: #28a745;
            background: #1e3a1e;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #d4af37;
            color: #000;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px 5px;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background: #b8941f;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Fix 262145 Currency Issue</h1>
            <p>Comprehensive solution for the currency display problem</p>
            <p><strong>Current Time:</strong> <?= date('Y-m-d H:i:s') ?></p>
        </div>

        <?php
        $applyFix = isset($_POST['apply_fix']) && $_POST['apply_fix'] === '1';
        
        if ($applyFix):
        ?>
        <!-- Apply Fix Section -->
        <div class="section success">
            <h2>🔧 Applying Comprehensive Fix</h2>
            <?php
            try {
                $database->beginTransaction();
                
                $fixesApplied = 0;
                $errors = [];
                
                echo "<h3>Step 1: Check for 262145 in database</h3>";
                
                // Check all tables for 262145 values
                $tables = [
                    'services' => ['price'],
                    'packages' => ['price', 'discount'],
                    'bookings' => ['total_amount'],
                    'payments' => ['amount'],
                    'service_variations' => ['price'],
                    'package_services' => ['price']
                ];
                
                $found262145 = false;
                foreach ($tables as $table => $columns) {
                    foreach ($columns as $column) {
                        try {
                            $records = $database->fetchAll("SELECT id, $column FROM $table WHERE $column = 262145");
                            if (!empty($records)) {
                                $found262145 = true;
                                echo "<p>❌ Found 262145 in $table.$column:</p>";
                                foreach ($records as $record) {
                                    echo "<p>  - ID {$record['id']}: {$record[$column]}</p>";
                                    // Fix the value - set to a reasonable default
                                    $newValue = 50000; // Default 50,000 TSH
                                    $database->execute("UPDATE $table SET $column = ? WHERE id = ?", [$newValue, $record['id']]);
                                    echo "<p>  ✓ Fixed to " . formatCurrency($newValue) . "</p>";
                                    $fixesApplied++;
                                }
                            }
                        } catch (Exception $e) {
                            $errors[] = "Error checking $table.$column: " . $e->getMessage();
                        }
                    }
                }
                
                if (!$found262145) {
                    echo "<p>✅ No 262145 values found in database</p>";
                }
                
                echo "<h3>Step 2: Check for NULL or invalid price values</h3>";
                
                // Fix NULL or 0 prices that might be causing issues
                $nullPriceFixes = [
                    'services' => 'price',
                    'packages' => 'price'
                ];
                
                foreach ($nullPriceFixes as $table => $column) {
                    try {
                        $nullRecords = $database->fetchAll("SELECT id, name, $column FROM $table WHERE $column IS NULL OR $column = 0");
                        if (!empty($nullRecords)) {
                            echo "<p>⚠️ Found NULL/0 prices in $table:</p>";
                            foreach ($nullRecords as $record) {
                                $defaultPrice = 50000; // 50,000 TSH default
                                $database->execute("UPDATE $table SET $column = ? WHERE id = ?", [$defaultPrice, $record['id']]);
                                echo "<p>  ✓ Fixed '{$record['name']}': NULL/0 → " . formatCurrency($defaultPrice) . "</p>";
                                $fixesApplied++;
                            }
                        } else {
                            echo "<p>✅ No NULL/0 prices found in $table</p>";
                        }
                    } catch (Exception $e) {
                        $errors[] = "Error fixing NULL prices in $table: " . $e->getMessage();
                    }
                }
                
                echo "<h3>Step 3: Check for extremely high prices</h3>";
                
                // Fix unreasonably high prices (over 10 million TSH)
                foreach ($nullPriceFixes as $table => $column) {
                    try {
                        $highPriceRecords = $database->fetchAll("SELECT id, name, $column FROM $table WHERE $column > 10000000");
                        if (!empty($highPriceRecords)) {
                            echo "<p>⚠️ Found extremely high prices in $table:</p>";
                            foreach ($highPriceRecords as $record) {
                                $reasonablePrice = 500000; // 500,000 TSH max
                                $database->execute("UPDATE $table SET $column = ? WHERE id = ?", [$reasonablePrice, $record['id']]);
                                echo "<p>  ✓ Fixed '{$record['name']}': " . formatCurrency($record[$column]) . " → " . formatCurrency($reasonablePrice) . "</p>";
                                $fixesApplied++;
                            }
                        } else {
                            echo "<p>✅ No extremely high prices found in $table</p>";
                        }
                    } catch (Exception $e) {
                        $errors[] = "Error fixing high prices in $table: " . $e->getMessage();
                    }
                }
                
                echo "<h3>Step 4: Add database constraints to prevent future issues</h3>";
                
                // Add constraints to prevent invalid prices
                try {
                    // Add check constraints for reasonable price ranges
                    $constraints = [
                        "ALTER TABLE services ADD CONSTRAINT chk_service_price CHECK (price >= 1000 AND price <= 5000000)",
                        "ALTER TABLE packages ADD CONSTRAINT chk_package_price CHECK (price >= 1000 AND price <= 10000000)",
                        "ALTER TABLE service_variations ADD CONSTRAINT chk_variation_price CHECK (price >= 1000 AND price <= 5000000)"
                    ];
                    
                    foreach ($constraints as $constraint) {
                        try {
                            $database->execute($constraint);
                            echo "<p>✅ Added price constraint</p>";
                        } catch (Exception $e) {
                            // Constraint might already exist, that's OK
                            if (strpos($e->getMessage(), 'already exists') === false && strpos($e->getMessage(), 'Duplicate') === false) {
                                echo "<p>⚠️ Constraint warning: " . htmlspecialchars($e->getMessage()) . "</p>";
                            }
                        }
                    }
                } catch (Exception $e) {
                    $errors[] = "Error adding constraints: " . $e->getMessage();
                }
                
                $database->commit();
                
                echo "<div class='success'>";
                echo "<h3>✅ Fix Applied Successfully!</h3>";
                echo "<p>Total fixes applied: $fixesApplied</p>";
                if (!empty($errors)) {
                    echo "<h4>Warnings/Errors:</h4>";
                    foreach ($errors as $error) {
                        echo "<p class='warning'>⚠️ $error</p>";
                    }
                }
                echo "</div>";
                
            } catch (Exception $e) {
                $database->rollback();
                echo "<div class='error'>";
                echo "<h3>❌ Error Applying Fix</h3>";
                echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
                echo "</div>";
            }
            ?>
        </div>
        <?php endif; ?>

        <!-- Current Status -->
        <div class="section">
            <h2>📊 Current Status Analysis</h2>
            <?php
            try {
                echo "<h3>Price Range Analysis:</h3>";
                
                $tables = ['services', 'packages'];
                foreach ($tables as $table) {
                    $stats = $database->fetch("
                        SELECT 
                            COUNT(*) as total_count,
                            MIN(price) as min_price,
                            MAX(price) as max_price,
                            AVG(price) as avg_price,
                            COUNT(CASE WHEN price = 262145 THEN 1 END) as count_262145,
                            COUNT(CASE WHEN price IS NULL OR price = 0 THEN 1 END) as count_null_zero
                        FROM $table 
                        WHERE is_active = 1
                    ");
                    
                    echo "<h4>$table Table:</h4>";
                    echo "<p>Total records: {$stats['total_count']}</p>";
                    echo "<p>Price range: " . formatCurrency($stats['min_price']) . " - " . formatCurrency($stats['max_price']) . "</p>";
                    echo "<p>Average price: " . formatCurrency($stats['avg_price']) . "</p>";
                    
                    if ($stats['count_262145'] > 0) {
                        echo "<p class='error'>❌ Records with 262145: {$stats['count_262145']}</p>";
                    } else {
                        echo "<p class='success'>✅ No 262145 values found</p>";
                    }
                    
                    if ($stats['count_null_zero'] > 0) {
                        echo "<p class='warning'>⚠️ Records with NULL/0 prices: {$stats['count_null_zero']}</p>";
                    } else {
                        echo "<p class='success'>✅ No NULL/0 prices found</p>";
                    }
                }
                
                echo "<h3>Sample Current Prices:</h3>";
                $samples = $database->fetchAll("
                    SELECT name, price, 'service' as type FROM services WHERE is_active = 1
                    UNION ALL
                    SELECT name, price, 'package' as type FROM packages WHERE is_active = 1
                    ORDER BY price ASC
                    LIMIT 10
                ");
                
                foreach ($samples as $sample) {
                    $status = ($sample['price'] == 262145) ? '❌' : '✅';
                    echo "<p>$status {$sample['type']}: {$sample['name']} - " . formatCurrency($sample['price']) . "</p>";
                }
                
            } catch (Exception $e) {
                echo "<div class='error'>";
                echo "<h3>❌ Error Analyzing Status</h3>";
                echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
                echo "</div>";
            }
            ?>
        </div>

        <!-- Actions -->
        <div class="section">
            <h2>🔧 Actions</h2>
            <?php if (!$applyFix): ?>
            <form method="POST" style="display: inline;">
                <input type="hidden" name="apply_fix" value="1">
                <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to apply the comprehensive fix? This will update any problematic price values in the database.')">
                    Apply Comprehensive Fix
                </button>
            </form>
            <?php endif; ?>
            
            <a href="/" class="btn">Test Homepage</a>
            <a href="/services.php" class="btn">Test Services</a>
            <a href="/packages.php" class="btn">Test Packages</a>
            <a href="/admin/" class="btn">Test Admin</a>
        </div>

        <!-- Manual Override -->
        <div class="section">
            <h2>🛠️ Manual Override</h2>
            <p>If the issue persists, you can manually set a specific price for testing:</p>
            
            <form method="POST">
                <input type="hidden" name="manual_override" value="1">
                <label>Service ID: <input type="number" name="service_id" placeholder="Enter service ID"></label>
                <label>New Price: <input type="number" name="new_price" placeholder="Enter price in TSH" value="50000"></label>
                <button type="submit" class="btn">Update Service Price</button>
            </form>
            
            <?php
            if (isset($_POST['manual_override']) && $_POST['manual_override'] === '1') {
                $serviceId = (int)$_POST['service_id'];
                $newPrice = (int)$_POST['new_price'];
                
                if ($serviceId > 0 && $newPrice > 0) {
                    try {
                        $service = $database->fetch("SELECT name FROM services WHERE id = ?", [$serviceId]);
                        if ($service) {
                            $database->execute("UPDATE services SET price = ? WHERE id = ?", [$newPrice, $serviceId]);
                            echo "<div class='success'>";
                            echo "<p>✅ Updated service '{$service['name']}' price to " . formatCurrency($newPrice) . "</p>";
                            echo "</div>";
                        } else {
                            echo "<div class='error'><p>❌ Service ID $serviceId not found</p></div>";
                        }
                    } catch (Exception $e) {
                        echo "<div class='error'><p>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p></div>";
                    }
                }
            }
            ?>
        </div>
    </div>
</body>
</html>
