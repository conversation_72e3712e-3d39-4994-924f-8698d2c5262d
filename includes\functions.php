<?php
/**
 * Common Functions
 * Flix Salonce - PHP Version
 */

/**
 * Sanitize input data
 */
function sanitize($data) {
    if (is_array($data)) {
        return array_map('sanitize', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Generate UUID v4
 */
function generateUUID() {
    if (function_exists('random_bytes')) {
        $data = random_bytes(16);
        $data[6] = chr(ord($data[6]) & 0x0f | 0x40); // set version to 0100
        $data[8] = chr(ord($data[8]) & 0x3f | 0x80); // set bits 6-7 to 10
        return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    } else {
        // Fallback for older PHP versions
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
}

/**
 * Format currency for TSH (no decimal places)
 */
function formatCurrency($amount, $currency = null) {
    if ($currency === null) {
        $currency = defined('CURRENCY_CODE') ? CURRENCY_CODE : 'TZS';
    }
    $symbol = defined('CURRENCY_SYMBOL') ? CURRENCY_SYMBOL : 'TSH';

    // Handle edge cases and ensure proper conversion
    if ($amount === null || $amount === '' || $amount === false) {
        $amount = 0;
    }

    // Convert to integer and handle the 262145 issue
    $amount = (int)$amount;

    // If we get the problematic value 262145, it might be a data corruption issue
    // Log it for debugging but still format it properly
    if ($amount === 262145) {
        error_log("formatCurrency: Detected suspicious value 262145 - possible data corruption");
    }

    // TSH currency doesn't use decimal places
    return $symbol . ' ' . number_format($amount);
}

/**
 * Format date
 */
function formatDate($date, $format = 'M j, Y') {
    return date($format, strtotime($date));
}

/**
 * Format time
 */
function formatTime($time, $format = 'g:i A') {
    return date($format, strtotime($time));
}

/**
 * Calculate time difference in minutes
 */
function getTimeDifferenceInMinutes($startTime, $endTime) {
    $start = strtotime($startTime);
    $end = strtotime($endTime);
    return ($end - $start) / 60;
}

/**
 * Generates a URL-friendly slug from a string.
 * Handles Unicode characters and provides a fallback for empty slugs.
 * @param string $text
 * @return string
 */
function generateSlug(string $text): string {
    // Remove Punctuation and Accents - \pL matches any kind of letter from any language, \d matches digits
    $text = preg_replace('~[^\pL\d]+~u', '-', $text);
    // Transliterate (convert non-ASCII characters to their closest ASCII equivalent)
    $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);
    // Remove unwanted characters (anything that's not a hyphen or alphanumeric)
    $text = preg_replace('~[^-\w]+~', '', $text);
    // Trim hyphens from the beginning and end
    $text = trim($text, '-');
    // Remove duplicate hyphens
    $text = preg_replace('~-+~', '-', $text);
    // Convert to lowercase
    $text = strtolower($text);

    if (empty($text)) {
        return 'n-a-' . substr(md5(time()), 0, 6); // Fallback for empty slugs
    }
    return $text;
}

// Removed the old uploadFile function from here.
// The new, more generic uploadFile function is in includes/upload_functions.php

/**
 * Send email using enhanced SMTP functionality
 * This function now uses the new email system for better reliability
 */
function sendEmail($to, $subject, $body, $isHtml = true) {
    // Use the enhanced SMTP email function if available
    if (function_exists('sendSMTPEmail')) {
        return sendSMTPEmail($to, $subject, $body, ['is_html' => $isHtml]);
    }

    // Fallback to basic mail() function if SMTP not available
    $headers = [
        'From: ' . APP_NAME . ' <' . SMTP_USERNAME . '>',
        'Reply-To: ' . SMTP_USERNAME,
        'X-Mailer: PHP/' . phpversion()
    ];

    if ($isHtml) {
        $headers[] = 'MIME-Version: 1.0';
        $headers[] = 'Content-type: text/html; charset=UTF-8';
    }

    return mail($to, $subject, $body, implode("\r\n", $headers));
}

/**
 * Generate random string
 */
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    
    return $randomString;
}

/**
 * Calculate points earned from amount
 */
function calculatePointsEarned($amount) {
    // 1 point per dollar spent
    return floor($amount);
}

/**
 * Calculate discount from points
 */
function calculatePointsDiscount($points) {
    // 100 points = TSH 1,000 discount
    return $points / 100;
}

/**
 * Get booking status badge class
 */
function getStatusBadgeClass($status) {
    switch (strtoupper($status)) {
        case 'PENDING':
            return 'bg-yellow-100 text-yellow-800';
        case 'CONFIRMED':
            return 'bg-blue-100 text-blue-800';
        case 'IN_PROGRESS':
            return 'bg-purple-100 text-purple-800';
        case 'COMPLETED':
            return 'bg-green-100 text-green-800';
        case 'CANCELLED':
            return 'bg-red-100 text-red-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
}

/**
 * Get payment status badge class
 */
function getPaymentStatusBadgeClass($status) {
    switch (strtoupper($status)) {
        case 'PENDING':
            return 'bg-yellow-100 text-yellow-800';
        case 'COMPLETED':
            return 'bg-green-100 text-green-800';
        case 'FAILED':
            return 'bg-red-100 text-red-800';
        case 'REFUNDED':
            return 'bg-gray-100 text-gray-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
}

/**
 * Check if time slot is available
 */
function isTimeSlotAvailable($date, $startTime, $endTime, $staffId = null, $excludeBookingId = null) {
    global $database;
    
    $sql = "SELECT COUNT(*) as count FROM bookings 
            WHERE date = ? 
            AND status NOT IN ('CANCELLED') 
            AND (
                (start_time <= ? AND end_time > ?) OR
                (start_time < ? AND end_time >= ?) OR
                (start_time >= ? AND start_time < ?)
            )";
    
    $params = [$date, $startTime, $startTime, $endTime, $endTime, $startTime, $endTime];
    
    if ($staffId) {
        $sql .= " AND staff_id = ?";
        $params[] = $staffId;
    }
    
    if ($excludeBookingId) {
        $sql .= " AND id != ?";
        $params[] = $excludeBookingId;
    }
    
    $result = $database->fetch($sql, $params);
    return $result['count'] == 0;
}

/**
 * Generate time slots for a day
 */
function generateTimeSlots($startTime = '09:00', $endTime = '18:00', $interval = 30) {
    $slots = [];
    $start = strtotime($startTime);
    $end = strtotime($endTime);
    
    while ($start < $end) {
        $slots[] = date('H:i', $start);
        $start = strtotime("+{$interval} minutes", $start);
    }
    
    return $slots;
}

/**
 * Log activity
 */
function logActivity($userId, $action, $details = '') {
    global $database;
    
    $database->query(
        "INSERT INTO activity_logs (user_id, action, details, created_at) VALUES (?, ?, ?, NOW())",
        [$userId, $action, $details]
    );
}

/**
 * Get setting value
 */
function getSetting($category, $key, $default = '') {
    global $database;
    
    $result = $database->fetch(
        "SELECT setting_value FROM settings WHERE category = ? AND setting_key = ?",
        [$category, $key]
    );
    
    return $result ? $result['setting_value'] : $default;
}

/**
 * Set setting value
 */
function setSetting($category, $key, $value) {
    global $database;
    
    $database->query(
        "INSERT INTO settings (category, setting_key, setting_value) 
         VALUES (?, ?, ?) 
         ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()",
        [$category, $key, $value]
    );
}

/**
 * Enhanced notification system
 */
function createNotification($userId, $title, $message, $type = 'GENERAL', $options = []) {
    global $database;

    // Determine category from type
    $category = getNotificationCategory($type);

    // Set default options
    $priority = $options['priority'] ?? 'MEDIUM';
    $actionUrl = $options['action_url'] ?? null;
    $metadata = $options['metadata'] ?? null;
    $expiresAt = $options['expires_at'] ?? null;

    // Convert metadata to JSON if it's an array
    if (is_array($metadata)) {
        $metadata = json_encode($metadata);
    }

    $notificationId = generateUUID();

    $database->query(
        "INSERT INTO notifications (id, user_id, title, message, type, category, priority, action_url, metadata, expires_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        [$notificationId, $userId, $title, $message, $type, $category, $priority, $actionUrl, $metadata, $expiresAt]
    );

    return $notificationId;
}

/**
 * Get notification category from type
 */
function getNotificationCategory($type) {
    $categoryMap = [
        'BOOKING_NEW' => 'BOOKING',
        'BOOKING_CONFIRMED' => 'BOOKING',
        'BOOKING_CANCELLED' => 'BOOKING',
        'BOOKING_COMPLETED' => 'BOOKING',
        'BOOKING_REMINDER' => 'BOOKING',
        'BOOKING_EXPIRED' => 'BOOKING',
        'BOOKING_NO_SHOW' => 'BOOKING',

        'CUSTOMER_NEW' => 'CUSTOMER',
        'CUSTOMER_BIRTHDAY' => 'CUSTOMER',
        'NEWSLETTER_SUBSCRIBER' => 'CUSTOMER',

        'PAYMENT_SUCCESS' => 'PAYMENT',
        'PAYMENT_FAILED' => 'PAYMENT',
        'REFUND_PROCESSED' => 'PAYMENT',

        'STAFF_NEW' => 'STAFF',
        'STAFF_SCHEDULE_CHANGE' => 'STAFF',
        'STAFF_LEAVE_REQUEST' => 'STAFF',

        'SYSTEM_MAINTENANCE' => 'SYSTEM',
        'SYSTEM_UPDATE' => 'SYSTEM',
        'SYSTEM_BACKUP' => 'SYSTEM',

        'PROMOTION_NEW' => 'MARKETING',
        'OFFER_EXPIRING' => 'MARKETING',
        'LOYALTY_MILESTONE' => 'MARKETING',

        'REVIEW_NEW' => 'FEEDBACK',
        'COMPLAINT_NEW' => 'FEEDBACK',

        'GENERAL' => 'SYSTEM'
    ];

    return $categoryMap[$type] ?? 'SYSTEM';
}

/**
 * Create booking notification
 */
function createBookingNotification($bookingId, $type, $additionalData = []) {
    global $database;

    // Get booking details
    $booking = $database->fetch(
        "SELECT b.*, u.name as customer_name, u.email as customer_email,
                s.name as service_name, p.name as package_name, st.name as staff_name
         FROM bookings b
         LEFT JOIN users u ON b.user_id = u.id
         LEFT JOIN services s ON b.service_id = s.id
         LEFT JOIN packages p ON b.package_id = p.id
         LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
         WHERE b.id = ?",
        [$bookingId]
    );

    if (!$booking) return false;

    $serviceName = $booking['service_name'] ?: $booking['package_name'] ?: 'Unknown Service';
    $customerName = $booking['customer_name'] ?: 'Unknown Customer';
    $staffName = $booking['staff_name'] ?: 'Unassigned';

    // Get all admin users
    $admins = $database->fetchAll("SELECT id FROM users WHERE role = 'ADMIN'");

    $notifications = [
        'BOOKING_NEW' => [
            'title' => 'New Booking Request',
            'message' => "{$customerName} has requested a booking for {$serviceName} on {$booking['date']} at {$booking['start_time']}",
            'priority' => 'HIGH'
        ],
        'BOOKING_CONFIRMED' => [
            'title' => 'Booking Confirmed',
            'message' => "Booking for {$customerName} ({$serviceName}) has been confirmed for {$booking['date']} at {$booking['start_time']}",
            'priority' => 'MEDIUM'
        ],
        'BOOKING_CANCELLED' => [
            'title' => 'Booking Cancelled',
            'message' => "Booking for {$customerName} ({$serviceName}) on {$booking['date']} has been cancelled",
            'priority' => 'MEDIUM'
        ],
        'BOOKING_COMPLETED' => [
            'title' => 'Booking Completed',
            'message' => "Booking for {$customerName} ({$serviceName}) has been completed successfully",
            'priority' => 'LOW'
        ],
        'BOOKING_REMINDER' => [
            'title' => 'Upcoming Booking Reminder',
            'message' => "Reminder: {$customerName} has a booking for {$serviceName} in 30 minutes",
            'priority' => 'HIGH'
        ],
        'BOOKING_EXPIRED' => [
            'title' => 'Booking Expired',
            'message' => "Booking for {$customerName} ({$serviceName}) on {$booking['date']} has expired",
            'priority' => 'MEDIUM'
        ],
        'BOOKING_NO_SHOW' => [
            'title' => 'Customer No-Show',
            'message' => "{$customerName} did not show up for their {$serviceName} appointment on {$booking['date']}",
            'priority' => 'MEDIUM'
        ]
    ];

    if (!isset($notifications[$type])) return false;

    $notificationData = $notifications[$type];
    $metadata = array_merge([
        'booking_id' => $bookingId,
        'customer_name' => $customerName,
        'service_name' => $serviceName,
        'staff_name' => $staffName,
        'booking_date' => $booking['date'],
        'booking_time' => $booking['start_time']
    ], $additionalData);

    $options = [
        'priority' => $notificationData['priority'],
        'action_url' => "/admin/bookings/view.php?id={$bookingId}",
        'metadata' => $metadata
    ];

    // Create notification for all admins
    foreach ($admins as $admin) {
        createNotification(
            $admin['id'],
            $notificationData['title'],
            $notificationData['message'],
            $type,
            $options
        );
    }

    return true;
}

/**
 * Create customer notification
 */
function createCustomerNotification($customerId, $type, $additionalData = []) {
    global $database;

    // Get customer details
    $customer = $database->fetch(
        "SELECT * FROM users WHERE id = ? AND role = 'CUSTOMER'",
        [$customerId]
    );

    if (!$customer) return false;

    // Get all admin users
    $admins = $database->fetchAll("SELECT id FROM users WHERE role = 'ADMIN'");

    $notifications = [
        'CUSTOMER_NEW' => [
            'title' => 'New Customer Registration',
            'message' => "New customer {$customer['name']} ({$customer['email']}) has registered",
            'priority' => 'MEDIUM'
        ],
        'CUSTOMER_BIRTHDAY' => [
            'title' => 'Customer Birthday',
            'message' => "It's {$customer['name']}'s birthday today! Consider sending a special offer",
            'priority' => 'LOW'
        ],
        'NEWSLETTER_SUBSCRIBER' => [
            'title' => 'New Newsletter Subscriber',
            'message' => "{$customer['email']} has subscribed to the newsletter",
            'priority' => 'LOW'
        ]
    ];

    if (!isset($notifications[$type])) return false;

    $notificationData = $notifications[$type];
    $metadata = array_merge([
        'customer_id' => $customerId,
        'customer_name' => $customer['name'],
        'customer_email' => $customer['email']
    ], $additionalData);

    $options = [
        'priority' => $notificationData['priority'],
        'action_url' => "/admin/customers/view.php?id={$customerId}",
        'metadata' => $metadata
    ];

    // Create notification for all admins
    foreach ($admins as $admin) {
        createNotification(
            $admin['id'],
            $notificationData['title'],
            $notificationData['message'],
            $type,
            $options
        );
    }

    return true;
}

/**
 * Create system notification
 */
function createSystemNotification($type, $message, $priority = 'MEDIUM', $additionalData = []) {
    global $database;

    // Get all admin users
    $admins = $database->fetchAll("SELECT id FROM users WHERE role = 'ADMIN'");

    $titles = [
        'SYSTEM_MAINTENANCE' => 'System Maintenance',
        'SYSTEM_UPDATE' => 'System Update',
        'SYSTEM_BACKUP' => 'System Backup',
        'PROMOTION_NEW' => 'New Promotion',
        'OFFER_EXPIRING' => 'Offer Expiring',
        'LOYALTY_MILESTONE' => 'Loyalty Milestone',
        'REVIEW_NEW' => 'New Review',
        'COMPLAINT_NEW' => 'New Complaint'
    ];

    $title = isset($titles[$type]) ? $titles[$type] : 'System Notification';

    $options = [
        'priority' => $priority,
        'metadata' => $additionalData
    ];

    // Create notification for all admins
    foreach ($admins as $admin) {
        createNotification(
            $admin['id'],
            $title,
            $message,
            $type,
            $options
        );
    }

    return true;
}

/**
 * JSON response helper
 */
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit();
}

/**
 * Pagination helper
 */
function paginate($totalItems, $itemsPerPage, $currentPage) {
    $totalPages = ceil($totalItems / $itemsPerPage);
    $offset = ($currentPage - 1) * $itemsPerPage;
    
    return [
        'total_items' => $totalItems,
        'items_per_page' => $itemsPerPage,
        'current_page' => $currentPage,
        'total_pages' => $totalPages,
        'offset' => $offset,
        'has_previous' => $currentPage > 1,
        'has_next' => $currentPage < $totalPages
    ];
}

/**
 * Generate a CSRF token and store it in the session.
 * @return string The generated token.
 */
function generateCsrfToken(): string {
    if (empty($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify a CSRF token against the one stored in the session.
 * @param string $token The token to verify.
 * @return bool True if the token is valid, false otherwise.
 */
function verifyCsrfToken(string $token): bool {
    if (isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token)) {
        // Token is valid, unset it to prevent reuse (optional, depends on strategy)
        // unset($_SESSION['csrf_token']);
        return true;
    }
    return false;
}

?>
