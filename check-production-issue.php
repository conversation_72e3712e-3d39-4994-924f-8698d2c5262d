<?php
/**
 * Check Production Issue
 * Test the actual production website to see where 262145 appears
 */

require_once __DIR__ . '/config/app.php';

header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check Production Issue - Flix Salon & SPA</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
            border-left: 4px solid #d4af37;
        }
        .section {
            background: #2a2a2a;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #d4af37;
        }
        .error {
            border-left-color: #dc3545;
            background: #3a1e1e;
        }
        .warning {
            border-left-color: #ffc107;
            background: #3a3a1e;
        }
        .success {
            border-left-color: #28a745;
            background: #1e3a1e;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #d4af37;
            color: #000;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px 5px;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background: #b8941f;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #444;
            border-radius: 5px;
        }
        .test-results {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Check Production Issue</h1>
            <p>Testing where 262145 appears on the production website</p>
            <p><strong>Current Time:</strong> <?= date('Y-m-d H:i:s') ?></p>
        </div>

        <!-- Quick Database Check -->
        <div class="section">
            <h2>🗄️ Quick Database Status</h2>
            <?php
            try {
                // Check if there are any 262145 values anywhere
                $tables = ['services', 'packages', 'bookings', 'payments'];
                $found = false;
                
                foreach ($tables as $table) {
                    $result = $database->query("SELECT * FROM $table WHERE CAST(CONCAT_WS('|', id, name, price, total_amount, amount) AS CHAR) LIKE '%262145%' LIMIT 1");
                    if ($result->rowCount() > 0) {
                        $found = true;
                        echo "<div class='error'>❌ Found 262145 in table: $table</div>";
                    }
                }
                
                if (!$found) {
                    echo "<div class='success'>✅ No 262145 values found in database</div>";
                }
                
                // Check current service prices
                echo "<h3>Current Service Prices (first 5):</h3>";
                $services = $database->fetchAll("SELECT name, price FROM services WHERE is_active = 1 ORDER BY price ASC LIMIT 5");
                foreach ($services as $service) {
                    echo "<p>- {$service['name']}: " . formatCurrency($service['price']) . "</p>";
                }
                
            } catch (Exception $e) {
                echo "<div class='error'>❌ Database Error: " . htmlspecialchars($e->getMessage()) . "</div>";
            }
            ?>
        </div>

        <!-- Test Pages -->
        <div class="section">
            <h2>🌐 Test Production Pages</h2>
            <p>Click the buttons below to test different pages and see if 262145 appears:</p>
            
            <a href="/" class="btn" target="_blank">Homepage</a>
            <a href="/services.php" class="btn" target="_blank">Services Page</a>
            <a href="/packages.php" class="btn" target="_blank">Packages Page</a>
            <a href="/admin/" class="btn" target="_blank">Admin Panel</a>
            <a href="/customer/" class="btn" target="_blank">Customer Panel</a>
            
            <h3>Test Results:</h3>
            <div class="test-results">
                <p><strong>Instructions:</strong></p>
                <ol>
                    <li>Click each button above to open the pages in new tabs</li>
                    <li>Look for any prices showing as "TSH 262,145"</li>
                    <li>Check the browser console for any JavaScript errors</li>
                    <li>Report back which specific pages show the issue</li>
                </ol>
            </div>
        </div>

        <!-- Settings Check -->
        <div class="section">
            <h2>⚙️ Settings and Configuration Check</h2>
            <?php
            try {
                // Check if there are any settings tables with 262145
                $settingsTables = ['settings', 'system_settings'];
                
                foreach ($settingsTables as $table) {
                    try {
                        $settings = $database->fetchAll("SELECT * FROM $table WHERE setting_value = '262145' OR setting_value = 262145");
                        if (!empty($settings)) {
                            echo "<div class='error'>";
                            echo "<h3>❌ Found 262145 in $table:</h3>";
                            foreach ($settings as $setting) {
                                echo "<p>Key: {$setting['setting_key']}, Value: {$setting['setting_value']}</p>";
                            }
                            echo "</div>";
                        } else {
                            echo "<div class='success'>✅ No 262145 found in $table</div>";
                        }
                    } catch (Exception $e) {
                        echo "<div class='warning'>⚠️ Table $table doesn't exist or error: " . htmlspecialchars($e->getMessage()) . "</div>";
                    }
                }
                
                // Check for any default price settings
                echo "<h3>Price-related Settings:</h3>";
                try {
                    $priceSettings = $database->fetchAll("SELECT * FROM settings WHERE setting_key LIKE '%price%' OR setting_key LIKE '%amount%' OR setting_key LIKE '%cost%'");
                    if (!empty($priceSettings)) {
                        foreach ($priceSettings as $setting) {
                            echo "<p>- {$setting['setting_key']}: {$setting['setting_value']}</p>";
                        }
                    } else {
                        echo "<p>No price-related settings found.</p>";
                    }
                } catch (Exception $e) {
                    echo "<p>No settings table or error: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
                
            } catch (Exception $e) {
                echo "<div class='error'>❌ Error checking settings: " . htmlspecialchars($e->getMessage()) . "</div>";
            }
            ?>
        </div>

        <!-- JavaScript Test -->
        <div class="section">
            <h2>🔧 Live JavaScript Test</h2>
            <div id="jsTest">
                <button onclick="runJavaScriptTest()" class="btn">Run JavaScript Test</button>
                <div id="jsResults"></div>
            </div>
            
            <script>
                function runJavaScriptTest() {
                    // Test the formatCurrency function that's used throughout the site
                    function formatCurrency(amount) {
                        return 'TSH ' + parseInt(amount).toLocaleString();
                    }
                    
                    const testValues = [0, 1000, 35000, 50000, 100000, 262145, 500000];
                    let results = '<h3>JavaScript formatCurrency Test Results:</h3>';
                    
                    testValues.forEach(value => {
                        const result = formatCurrency(value);
                        const status = (result === 'TSH 262,145') ? '⚠️ PROBLEM' : '✅ OK';
                        results += `<p>${value} → ${result} ${status}</p>`;
                    });
                    
                    // Test if there are any global variables with 262145
                    results += '<h3>Global Variable Check:</h3>';
                    
                    // Check window object for any 262145 values
                    let found262145 = false;
                    for (let key in window) {
                        try {
                            if (window[key] === 262145 || window[key] === '262145') {
                                results += `<p class="error">❌ Found 262145 in window.${key}</p>`;
                                found262145 = true;
                            }
                        } catch (e) {
                            // Ignore errors accessing window properties
                        }
                    }
                    
                    if (!found262145) {
                        results += '<p class="success">✅ No 262145 found in global variables</p>';
                    }
                    
                    document.getElementById('jsResults').innerHTML = results;
                }
            </script>
        </div>

        <!-- Manual Test Form -->
        <div class="section">
            <h2>🧪 Manual Currency Test</h2>
            <p>Test the formatCurrency function with custom values:</p>
            
            <input type="number" id="testAmount" placeholder="Enter amount" value="50000">
            <button onclick="testCustomAmount()" class="btn">Test Amount</button>
            
            <div id="customTestResult"></div>
            
            <script>
                function testCustomAmount() {
                    const amount = document.getElementById('testAmount').value;
                    
                    // Test PHP formatCurrency (via AJAX)
                    fetch('<?= $_SERVER['PHP_SELF'] ?>?test_amount=' + amount)
                        .then(response => response.text())
                        .then(data => {
                            // Extract just the formatted result
                            const match = data.match(/PHP Result: ([^<]+)/);
                            const phpResult = match ? match[1] : 'Error';
                            
                            // Test JavaScript formatCurrency
                            function formatCurrency(amount) {
                                return 'TSH ' + parseInt(amount).toLocaleString();
                            }
                            const jsResult = formatCurrency(amount);
                            
                            document.getElementById('customTestResult').innerHTML = `
                                <h3>Test Results for ${amount}:</h3>
                                <p><strong>PHP formatCurrency:</strong> ${phpResult}</p>
                                <p><strong>JavaScript formatCurrency:</strong> ${jsResult}</p>
                                <p><strong>Match:</strong> ${phpResult === jsResult ? '✅ Yes' : '❌ No'}</p>
                            `;
                        })
                        .catch(error => {
                            document.getElementById('customTestResult').innerHTML = `<p class="error">Error: ${error}</p>`;
                        });
                }
            </script>
            
            <?php
            // Handle AJAX test request
            if (isset($_GET['test_amount'])) {
                $testAmount = (int)$_GET['test_amount'];
                echo "PHP Result: " . formatCurrency($testAmount);
                exit;
            }
            ?>
        </div>
    </div>
</body>
</html>
