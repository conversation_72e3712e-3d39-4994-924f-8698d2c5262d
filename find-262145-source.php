<?php
/**
 * Find 262145 Source
 * Comprehensive search for where 262145 is coming from
 */

require_once __DIR__ . '/config/app.php';

header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Find 262145 Source - Flix Salon & SPA</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
            border-left: 4px solid #d4af37;
        }
        .section {
            background: #2a2a2a;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #d4af37;
        }
        .error {
            border-left-color: #dc3545;
            background: #3a1e1e;
        }
        .warning {
            border-left-color: #ffc107;
            background: #3a3a1e;
        }
        .success {
            border-left-color: #28a745;
            background: #1e3a1e;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #444;
            padding: 8px;
            text-align: left;
            font-size: 12px;
        }
        th {
            background: #333;
        }
        .highlight {
            background: #ffc107;
            color: #000;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .code {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Find 262145 Source</h1>
            <p>Comprehensive search for the source of 262145 currency display</p>
            <p><strong>Current Time:</strong> <?= date('Y-m-d H:i:s') ?></p>
        </div>

        <!-- Check Database for 262145 -->
        <div class="section">
            <h2>🗄️ Database Search for 262145</h2>
            <?php
            try {
                $found262145 = false;
                $tables = [
                    'services' => ['price'],
                    'packages' => ['price', 'discount'],
                    'bookings' => ['total_amount'],
                    'payments' => ['amount'],
                    'service_variations' => ['price'],
                    'package_services' => ['price'],
                    'settings' => ['setting_value'],
                    'system_settings' => ['setting_value']
                ];
                
                echo "<table>";
                echo "<tr><th>Table</th><th>Column</th><th>Count</th><th>Sample Records</th></tr>";
                
                foreach ($tables as $table => $columns) {
                    foreach ($columns as $column) {
                        try {
                            $count = $database->fetch("SELECT COUNT(*) as count FROM $table WHERE $column = 262145");
                            $records = $database->fetchAll("SELECT * FROM $table WHERE $column = 262145 LIMIT 3");
                            
                            $class = $count['count'] > 0 ? 'class="highlight"' : '';
                            if ($count['count'] > 0) $found262145 = true;
                            
                            echo "<tr $class>";
                            echo "<td>$table</td>";
                            echo "<td>$column</td>";
                            echo "<td>{$count['count']}</td>";
                            echo "<td>";
                            if (!empty($records)) {
                                foreach ($records as $record) {
                                    echo "ID: {$record['id']}, $column: {$record[$column]}<br>";
                                }
                            } else {
                                echo "None";
                            }
                            echo "</td>";
                            echo "</tr>";
                        } catch (Exception $e) {
                            echo "<tr>";
                            echo "<td>$table</td>";
                            echo "<td>$column</td>";
                            echo "<td>ERROR</td>";
                            echo "<td>" . htmlspecialchars($e->getMessage()) . "</td>";
                            echo "</tr>";
                        }
                    }
                }
                echo "</table>";
                
                if (!$found262145) {
                    echo "<div class='success'><h3>✅ No 262145 values found in database!</h3></div>";
                } else {
                    echo "<div class='error'><h3>❌ Found 262145 values in database!</h3></div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='error'>";
                echo "<h3>❌ Error Searching Database</h3>";
                echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
                echo "</div>";
            }
            ?>
        </div>

        <!-- Check Constants and Configuration -->
        <div class="section">
            <h2>⚙️ Configuration Check</h2>
            <?php
            echo "<table>";
            echo "<tr><th>Constant</th><th>Value</th><th>Status</th></tr>";
            
            $constants = [
                'CURRENCY_CODE' => defined('CURRENCY_CODE') ? CURRENCY_CODE : 'NOT DEFINED',
                'CURRENCY_SYMBOL' => defined('CURRENCY_SYMBOL') ? CURRENCY_SYMBOL : 'NOT DEFINED',
                'APP_NAME' => defined('APP_NAME') ? APP_NAME : 'NOT DEFINED',
                'APP_URL' => defined('APP_URL') ? APP_URL : 'NOT DEFINED'
            ];
            
            foreach ($constants as $name => $value) {
                $status = ($value === 'NOT DEFINED') ? '❌ Missing' : '✅ OK';
                $class = ($value === 'NOT DEFINED') ? 'class="highlight"' : '';
                
                echo "<tr $class>";
                echo "<td>$name</td>";
                echo "<td>$value</td>";
                echo "<td>$status</td>";
                echo "</tr>";
            }
            echo "</table>";
            ?>
        </div>

        <!-- Test formatCurrency Function -->
        <div class="section">
            <h2>🧪 Test formatCurrency Function</h2>
            <?php
            echo "<table>";
            echo "<tr><th>Input</th><th>Output</th><th>Status</th></tr>";
            
            $testValues = [
                null,
                '',
                0,
                1000,
                35000,
                50000,
                100000,
                262145,
                500000,
                '262145',
                'invalid'
            ];
            
            foreach ($testValues as $value) {
                try {
                    $result = formatCurrency($value);
                    $status = ($result === 'TSH 262,145') ? '⚠️ Shows 262145' : '✅ OK';
                    $class = ($result === 'TSH 262,145') ? 'class="highlight"' : '';
                    
                    echo "<tr $class>";
                    echo "<td>" . var_export($value, true) . "</td>";
                    echo "<td>$result</td>";
                    echo "<td>$status</td>";
                    echo "</tr>";
                } catch (Exception $e) {
                    echo "<tr class='highlight'>";
                    echo "<td>" . var_export($value, true) . "</td>";
                    echo "<td>ERROR: " . htmlspecialchars($e->getMessage()) . "</td>";
                    echo "<td>❌ Error</td>";
                    echo "</tr>";
                }
            }
            echo "</table>";
            ?>
        </div>

        <!-- Check for Global Variables -->
        <div class="section">
            <h2>🌐 Global Variables Check</h2>
            <?php
            echo "<h3>$_SESSION Variables:</h3>";
            echo "<div class='code'>";
            if (!empty($_SESSION)) {
                foreach ($_SESSION as $key => $value) {
                    if (is_string($value) && strpos($value, '262145') !== false) {
                        echo "<span class='highlight'>$key: " . htmlspecialchars($value) . "</span>\n";
                    } else {
                        echo "$key: " . htmlspecialchars(print_r($value, true)) . "\n";
                    }
                }
            } else {
                echo "No session variables found.";
            }
            echo "</div>";
            
            echo "<h3>$_COOKIE Variables:</h3>";
            echo "<div class='code'>";
            if (!empty($_COOKIE)) {
                foreach ($_COOKIE as $key => $value) {
                    if (is_string($value) && strpos($value, '262145') !== false) {
                        echo "<span class='highlight'>$key: " . htmlspecialchars($value) . "</span>\n";
                    } else {
                        echo "$key: " . htmlspecialchars($value) . "\n";
                    }
                }
            } else {
                echo "No cookies found.";
            }
            echo "</div>";
            ?>
        </div>

        <!-- Sample Data from Key Tables -->
        <div class="section">
            <h2>📊 Sample Data Analysis</h2>
            <?php
            try {
                echo "<h3>Recent Services:</h3>";
                $services = $database->fetchAll("SELECT id, name, price FROM services WHERE is_active = 1 ORDER BY id DESC LIMIT 5");
                echo "<table>";
                echo "<tr><th>ID</th><th>Name</th><th>Raw Price</th><th>Formatted Price</th></tr>";
                foreach ($services as $service) {
                    $formatted = formatCurrency($service['price']);
                    $class = ($formatted === 'TSH 262,145') ? 'class="highlight"' : '';
                    echo "<tr $class>";
                    echo "<td>{$service['id']}</td>";
                    echo "<td>{$service['name']}</td>";
                    echo "<td>{$service['price']}</td>";
                    echo "<td>$formatted</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                echo "<h3>Recent Packages:</h3>";
                $packages = $database->fetchAll("SELECT id, name, price FROM packages WHERE is_active = 1 ORDER BY id DESC LIMIT 5");
                echo "<table>";
                echo "<tr><th>ID</th><th>Name</th><th>Raw Price</th><th>Formatted Price</th></tr>";
                foreach ($packages as $package) {
                    $formatted = formatCurrency($package['price']);
                    $class = ($formatted === 'TSH 262,145') ? 'class="highlight"' : '';
                    echo "<tr $class>";
                    echo "<td>{$package['id']}</td>";
                    echo "<td>{$package['name']}</td>";
                    echo "<td>{$package['price']}</td>";
                    echo "<td>$formatted</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
            } catch (Exception $e) {
                echo "<div class='error'>";
                echo "<h3>❌ Error Getting Sample Data</h3>";
                echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
                echo "</div>";
            }
            ?>
        </div>

        <!-- JavaScript Test -->
        <div class="section">
            <h2>🔧 JavaScript Currency Test</h2>
            <div id="jsTestResults"></div>
            <script>
                function testJavaScriptCurrency() {
                    const testValues = [0, 1000, 35000, 50000, 100000, 262145, 500000];
                    let html = '<table><tr><th>Input</th><th>JS formatCurrency Output</th><th>Status</th></tr>';
                    
                    // Define the formatCurrency function as it appears in the codebase
                    function formatCurrency(amount) {
                        return 'TSH ' + parseInt(amount).toLocaleString();
                    }
                    
                    testValues.forEach(value => {
                        const result = formatCurrency(value);
                        const status = (result === 'TSH 262,145') ? '⚠️ Shows 262145' : '✅ OK';
                        const className = (result === 'TSH 262,145') ? 'highlight' : '';
                        
                        html += `<tr class="${className}">`;
                        html += `<td>${value}</td>`;
                        html += `<td>${result}</td>`;
                        html += `<td>${status}</td>`;
                        html += `</tr>`;
                    });
                    
                    html += '</table>';
                    document.getElementById('jsTestResults').innerHTML = html;
                }
                
                // Run the test
                testJavaScriptCurrency();
            </script>
        </div>
    </div>
</body>
</html>
