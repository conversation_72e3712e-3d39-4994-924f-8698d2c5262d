<?php
/**
 * Error Log Viewer
 * Simple tool to view PHP error logs in real-time
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Security check - only allow in development or with admin access
$isProduction = isset($_SERVER['HTTP_HOST']) && 
               (strpos($_SERVER['HTTP_HOST'], 'flix.co.tz') !== false ||
                strpos($_SERVER['HTTP_HOST'], 'www.flix.co.tz') !== false);

if ($isProduction) {
    // In production, require admin login
    session_start();
    if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'ADMIN') {
        die("Access denied. Admin login required.");
    }
}

$logFiles = [
    'logs/error.log' => 'PHP Error Log',
    'logs/debug.log' => 'Debug Log',
    'logs/access.log' => 'Access Log'
];

$selectedLog = $_GET['log'] ?? 'logs/error.log';
$lines = intval($_GET['lines'] ?? 50);
$refresh = intval($_GET['refresh'] ?? 0);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Log Viewer - Flix Salon & SPA</title>
    <?php if ($refresh > 0): ?>
    <meta http-equiv="refresh" content="<?= $refresh ?>">
    <?php endif; ?>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            font-size: 12px;
        }
        .header {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #d4af37;
        }
        .controls {
            background: #2a2a2a;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        .log-content {
            background: #000;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #444;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            line-height: 1.4;
            max-height: 80vh;
            overflow-y: auto;
        }
        .error-line {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #dc3545;
            background: rgba(220, 53, 69, 0.1);
        }
        .warning-line {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #ffc107;
            background: rgba(255, 193, 7, 0.1);
        }
        .notice-line {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #17a2b8;
            background: rgba(23, 162, 184, 0.1);
        }
        select, input, button {
            padding: 8px 12px;
            background: #333;
            color: #fff;
            border: 1px solid #555;
            border-radius: 5px;
        }
        button {
            background: #d4af37;
            color: #000;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background: #b8941f;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }
        .stat {
            background: #333;
            padding: 10px 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #d4af37;
        }
        .timestamp {
            color: #888;
        }
        .file-path {
            color: #6c757d;
        }
        .line-number {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📋 Error Log Viewer</h1>
        <p>Real-time PHP error monitoring for Flix Salon & SPA</p>
        <p><strong>Current Time:</strong> <?= date('Y-m-d H:i:s') ?></p>
    </div>

    <div class="controls">
        <form method="GET" style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
            <label>
                Log File:
                <select name="log">
                    <?php foreach ($logFiles as $file => $name): ?>
                        <option value="<?= $file ?>" <?= $selectedLog === $file ? 'selected' : '' ?>>
                            <?= $name ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </label>
            
            <label>
                Lines:
                <select name="lines">
                    <option value="25" <?= $lines === 25 ? 'selected' : '' ?>>25</option>
                    <option value="50" <?= $lines === 50 ? 'selected' : '' ?>>50</option>
                    <option value="100" <?= $lines === 100 ? 'selected' : '' ?>>100</option>
                    <option value="200" <?= $lines === 200 ? 'selected' : '' ?>>200</option>
                    <option value="500" <?= $lines === 500 ? 'selected' : '' ?>>500</option>
                </select>
            </label>
            
            <label>
                Auto-refresh:
                <select name="refresh">
                    <option value="0" <?= $refresh === 0 ? 'selected' : '' ?>>Off</option>
                    <option value="5" <?= $refresh === 5 ? 'selected' : '' ?>>5 seconds</option>
                    <option value="10" <?= $refresh === 10 ? 'selected' : '' ?>>10 seconds</option>
                    <option value="30" <?= $refresh === 30 ? 'selected' : '' ?>>30 seconds</option>
                </select>
            </label>
            
            <button type="submit">Update</button>
        </form>
        
        <a href="?clear=<?= urlencode($selectedLog) ?>" onclick="return confirm('Clear this log file?')" style="background: #dc3545; color: white; padding: 8px 12px; text-decoration: none; border-radius: 5px;">Clear Log</a>
    </div>

    <?php
    // Handle log clearing
    if (isset($_GET['clear']) && file_exists($_GET['clear'])) {
        file_put_contents($_GET['clear'], '');
        echo "<div style='background: #28a745; color: white; padding: 10px; border-radius: 5px; margin-bottom: 20px;'>Log file cleared successfully!</div>";
    }

    // Read and display log file
    if (file_exists($selectedLog)) {
        $content = file_get_contents($selectedLog);
        $allLines = explode("\n", $content);
        $totalLines = count($allLines);
        $recentLines = array_slice($allLines, -$lines);
        
        // Calculate stats
        $errorCount = 0;
        $warningCount = 0;
        $noticeCount = 0;
        
        foreach ($recentLines as $line) {
            if (stripos($line, 'error') !== false || stripos($line, 'fatal') !== false) {
                $errorCount++;
            } elseif (stripos($line, 'warning') !== false) {
                $warningCount++;
            } elseif (stripos($line, 'notice') !== false) {
                $noticeCount++;
            }
        }
        
        echo "<div class='stats'>";
        echo "<div class='stat'><div class='stat-value'>$totalLines</div><div>Total Lines</div></div>";
        echo "<div class='stat'><div class='stat-value'>$errorCount</div><div>Errors</div></div>";
        echo "<div class='stat'><div class='stat-value'>$warningCount</div><div>Warnings</div></div>";
        echo "<div class='stat'><div class='stat-value'>$noticeCount</div><div>Notices</div></div>";
        echo "</div>";
        
        echo "<div class='log-content'>";
        
        if (empty(trim($content))) {
            echo "<div style='text-align: center; color: #888; padding: 40px;'>Log file is empty</div>";
        } else {
            foreach ($recentLines as $line) {
                if (empty(trim($line))) continue;
                
                $class = '';
                if (stripos($line, 'error') !== false || stripos($line, 'fatal') !== false) {
                    $class = 'error-line';
                } elseif (stripos($line, 'warning') !== false) {
                    $class = 'warning-line';
                } elseif (stripos($line, 'notice') !== false) {
                    $class = 'notice-line';
                }
                
                // Highlight timestamps, file paths, and line numbers
                $line = preg_replace('/\[([\d\-\s:]+)\]/', '<span class="timestamp">[$1]</span>', $line);
                $line = preg_replace('/in (\/[^\s]+)/', 'in <span class="file-path">$1</span>', $line);
                $line = preg_replace('/on line (\d+)/', 'on line <span class="line-number">$1</span>', $line);
                
                echo "<div class='$class'>" . htmlspecialchars($line) . "</div>";
            }
        }
        
        echo "</div>";
        
        echo "<p style='margin-top: 20px; color: #888;'>Showing last $lines lines of " . number_format($totalLines) . " total lines</p>";
        
    } else {
        echo "<div style='background: #dc3545; color: white; padding: 20px; border-radius: 10px;'>";
        echo "Log file not found: " . htmlspecialchars($selectedLog);
        echo "</div>";
    }
    ?>

    <div style="margin-top: 30px; text-align: center;">
        <a href="debug-errors.php" style="color: #d4af37; text-decoration: none;">← Back to Debug Page</a> |
        <a href="test-functions.php" style="color: #d4af37; text-decoration: none;">Function Tests</a> |
        <a href="/" style="color: #d4af37; text-decoration: none;">Homepage</a>
    </div>
</body>
</html>
