<?php
/**
 * Test Live Site
 * Fetch and analyze the actual production website to find 262145
 */

header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Live Site - Find 262145</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
            border-left: 4px solid #d4af37;
        }
        .section {
            background: #2a2a2a;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #d4af37;
        }
        .error {
            border-left-color: #dc3545;
            background: #3a1e1e;
        }
        .warning {
            border-left-color: #ffc107;
            background: #3a3a1e;
        }
        .success {
            border-left-color: #28a745;
            background: #1e3a1e;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #d4af37;
            color: #000;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px 5px;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background: #b8941f;
        }
        .code {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
        }
        .highlight {
            background: #ffc107;
            color: #000;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Test Live Site for 262145</h1>
            <p>Fetching and analyzing the production website to find where 262145 appears</p>
            <p><strong>Current Time:</strong> <?= date('Y-m-d H:i:s') ?></p>
        </div>

        <!-- Test Production Pages -->
        <div class="section">
            <h2>🌐 Production Website Analysis</h2>
            
            <?php
            $productionUrls = [
                'Homepage' => 'https://flix.co.tz/flix/',
                'Services' => 'https://flix.co.tz/flix/services.php',
                'Packages' => 'https://flix.co.tz/flix/packages.php',
                'Admin Login' => 'https://flix.co.tz/flix/admin/',
            ];
            
            foreach ($productionUrls as $name => $url) {
                echo "<h3>Testing: $name</h3>";
                
                try {
                    // Set up context for the request
                    $context = stream_context_create([
                        'http' => [
                            'method' => 'GET',
                            'header' => [
                                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                                'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                                'Accept-Language: en-US,en;q=0.5',
                                'Accept-Encoding: gzip, deflate',
                                'Connection: keep-alive',
                            ],
                            'timeout' => 30,
                        ]
                    ]);
                    
                    // Fetch the page content
                    $content = @file_get_contents($url, false, $context);
                    
                    if ($content === false) {
                        echo "<div class='error'>❌ Failed to fetch $url</div>";
                        continue;
                    }
                    
                    // Check if 262145 appears in the content
                    $count262145 = substr_count($content, '262145');
                    $countTSH262145 = substr_count($content, 'TSH 262,145');
                    
                    if ($count262145 > 0 || $countTSH262145 > 0) {
                        echo "<div class='error'>";
                        echo "<h4>❌ FOUND 262145 on $name!</h4>";
                        echo "<p>Raw '262145' occurrences: $count262145</p>";
                        echo "<p>Formatted 'TSH 262,145' occurrences: $countTSH262145</p>";
                        
                        // Extract context around 262145
                        $lines = explode("\n", $content);
                        $foundLines = [];
                        
                        foreach ($lines as $lineNum => $line) {
                            if (strpos($line, '262145') !== false) {
                                $foundLines[] = [
                                    'line' => $lineNum + 1,
                                    'content' => trim($line)
                                ];
                            }
                        }
                        
                        if (!empty($foundLines)) {
                            echo "<h5>Context where 262145 appears:</h5>";
                            echo "<div class='code'>";
                            foreach ($foundLines as $found) {
                                $highlightedLine = str_replace('262145', '<span class="highlight">262145</span>', htmlspecialchars($found['content']));
                                echo "Line {$found['line']}: $highlightedLine\n";
                            }
                            echo "</div>";
                        }
                        echo "</div>";
                    } else {
                        echo "<div class='success'>✅ No 262145 found on $name</div>";
                    }
                    
                    // Check for common price patterns
                    $pricePatterns = [
                        '/TSH\s+[\d,]+/' => 'TSH prices',
                        '/price["\']?\s*:\s*[\d,]+/' => 'Price values',
                        '/amount["\']?\s*:\s*[\d,]+/' => 'Amount values',
                    ];
                    
                    echo "<h4>Price Analysis for $name:</h4>";
                    foreach ($pricePatterns as $pattern => $description) {
                        preg_match_all($pattern, $content, $matches);
                        if (!empty($matches[0])) {
                            $uniqueMatches = array_unique($matches[0]);
                            echo "<p><strong>$description:</strong> " . implode(', ', array_slice($uniqueMatches, 0, 5));
                            if (count($uniqueMatches) > 5) {
                                echo " (and " . (count($uniqueMatches) - 5) . " more)";
                            }
                            echo "</p>";
                        }
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='error'>❌ Error fetching $name: " . htmlspecialchars($e->getMessage()) . "</div>";
                }
                
                echo "<hr>";
            }
            ?>
        </div>

        <!-- JavaScript Analysis -->
        <div class="section">
            <h2>🔧 JavaScript Analysis</h2>
            <p>Let's test if the issue is in JavaScript currency formatting:</p>
            
            <button onclick="analyzeJavaScript()" class="btn">Analyze JavaScript</button>
            <div id="jsAnalysis"></div>
            
            <script>
                function analyzeJavaScript() {
                    let results = '<h3>JavaScript Analysis Results:</h3>';
                    
                    // Test the formatCurrency function
                    function formatCurrency(amount) {
                        return 'TSH ' + parseInt(amount).toLocaleString();
                    }
                    
                    // Test various inputs
                    const testInputs = [
                        null,
                        undefined,
                        '',
                        '0',
                        0,
                        '1000',
                        1000,
                        '35000',
                        35000,
                        '262145',
                        262145,
                        'invalid',
                        NaN,
                        Infinity,
                        -1000
                    ];
                    
                    results += '<table border="1" style="border-collapse: collapse; width: 100%;">';
                    results += '<tr><th>Input</th><th>Type</th><th>parseInt(input)</th><th>formatCurrency Result</th><th>Status</th></tr>';
                    
                    testInputs.forEach(input => {
                        const inputType = typeof input;
                        const parsedInt = parseInt(input);
                        const formatted = formatCurrency(input);
                        const status = formatted.includes('262,145') ? '❌ PROBLEM' : '✅ OK';
                        const rowClass = formatted.includes('262,145') ? 'style="background-color: #ffc107; color: #000;"' : '';
                        
                        results += `<tr ${rowClass}>`;
                        results += `<td>${input === null ? 'null' : input === undefined ? 'undefined' : input}</td>`;
                        results += `<td>${inputType}</td>`;
                        results += `<td>${parsedInt}</td>`;
                        results += `<td>${formatted}</td>`;
                        results += `<td>${status}</td>`;
                        results += '</tr>';
                    });
                    
                    results += '</table>';
                    
                    // Check for global variables that might contain 262145
                    results += '<h3>Global Variable Check:</h3>';
                    let foundGlobal = false;
                    
                    // Common variable names that might contain prices
                    const priceVars = ['price', 'amount', 'total', 'cost', 'fee', 'charge'];
                    
                    priceVars.forEach(varName => {
                        if (window[varName] !== undefined) {
                            const value = window[varName];
                            if (value === 262145 || value === '262145') {
                                results += `<p class="error">❌ Found 262145 in window.${varName}</p>`;
                                foundGlobal = true;
                            } else {
                                results += `<p>window.${varName} = ${value}</p>`;
                            }
                        }
                    });
                    
                    if (!foundGlobal) {
                        results += '<p class="success">✅ No 262145 found in common global variables</p>';
                    }
                    
                    // Test parseInt behavior with edge cases
                    results += '<h3>parseInt Edge Cases:</h3>';
                    const edgeCases = ['', null, undefined, 'abc', '123abc', '262145.99', '262145px'];
                    
                    edgeCases.forEach(testCase => {
                        const result = parseInt(testCase);
                        const status = result === 262145 ? '❌ PRODUCES 262145' : '✅ OK';
                        results += `<p>parseInt(${testCase}) = ${result} ${status}</p>`;
                    });
                    
                    document.getElementById('jsAnalysis').innerHTML = results;
                }
            </script>
        </div>

        <!-- Manual Test -->
        <div class="section">
            <h2>🧪 Manual Test</h2>
            <p>Test specific scenarios that might cause 262145:</p>
            
            <div>
                <h3>Test Scenario 1: Empty/Null Values</h3>
                <button onclick="testEmptyValues()" class="btn">Test Empty Values</button>
                <div id="emptyTest"></div>
            </div>
            
            <div>
                <h3>Test Scenario 2: String Conversion</h3>
                <button onclick="testStringConversion()" class="btn">Test String Conversion</button>
                <div id="stringTest"></div>
            </div>
            
            <script>
                function testEmptyValues() {
                    function formatCurrency(amount) {
                        return 'TSH ' + parseInt(amount).toLocaleString();
                    }
                    
                    const emptyValues = [null, undefined, '', '0', 0, false, true, [], {}];
                    let results = '<h4>Empty Value Test Results:</h4>';
                    
                    emptyValues.forEach(value => {
                        const formatted = formatCurrency(value);
                        const status = formatted.includes('262,145') ? '❌ PROBLEM' : '✅ OK';
                        results += `<p>${JSON.stringify(value)} → ${formatted} ${status}</p>`;
                    });
                    
                    document.getElementById('emptyTest').innerHTML = results;
                }
                
                function testStringConversion() {
                    function formatCurrency(amount) {
                        return 'TSH ' + parseInt(amount).toLocaleString();
                    }
                    
                    const stringValues = ['262145', '262145.00', '262145px', 'TSH 262145', 'price: 262145'];
                    let results = '<h4>String Conversion Test Results:</h4>';
                    
                    stringValues.forEach(value => {
                        const formatted = formatCurrency(value);
                        const status = formatted.includes('262,145') ? '❌ PROBLEM' : '✅ OK';
                        results += `<p>"${value}" → ${formatted} ${status}</p>`;
                    });
                    
                    document.getElementById('stringTest').innerHTML = results;
                }
            </script>
        </div>

        <!-- Next Steps -->
        <div class="section">
            <h2>📋 Next Steps</h2>
            <p>Based on the analysis above:</p>
            <ol>
                <li>If 262145 is found on the production website, note which pages and where</li>
                <li>Check if it's in the HTML source or generated by JavaScript</li>
                <li>Look for patterns in the context where it appears</li>
                <li>Check if it's related to specific services, packages, or calculations</li>
            </ol>
            
            <p><strong>Report your findings:</strong> Tell me exactly which pages show 262145 and in what context.</p>
        </div>
    </div>
</body>
</html>
